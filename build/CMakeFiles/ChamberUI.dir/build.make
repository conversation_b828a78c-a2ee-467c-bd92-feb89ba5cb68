# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake

# The command to remove a file.
RM = /Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/X/soloholic/ChamberUI3

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/X/soloholic/ChamberUI3/build

# Include any dependencies generated for this target.
include CMakeFiles/ChamberUI.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/ChamberUI.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/ChamberUI.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/ChamberUI.dir/flags.make

.qt/rcc/qrc_qml_resources.cpp: /Users/<USER>/X/soloholic/ChamberUI3/qml/main.qml
.qt/rcc/qrc_qml_resources.cpp: /Users/<USER>/X/soloholic/ChamberUI3/qml/components/ChatView.qml
.qt/rcc/qrc_qml_resources.cpp: /Users/<USER>/X/soloholic/ChamberUI3/qml/components/SessionList.qml
.qt/rcc/qrc_qml_resources.cpp: /Users/<USER>/X/soloholic/ChamberUI3/qml/components/MessageBubble.qml
.qt/rcc/qrc_qml_resources.cpp: /Users/<USER>/X/soloholic/ChamberUI3/qml/components/InputArea.qml
.qt/rcc/qrc_qml_resources.cpp: /Users/<USER>/X/soloholic/ChamberUI3/qml/styles/Colors.qml
.qt/rcc/qrc_qml_resources.cpp: /Users/<USER>/X/soloholic/ChamberUI3/qml/styles/Typography.qml
.qt/rcc/qrc_qml_resources.cpp: .qt/rcc/qml_resources.qrc
.qt/rcc/qrc_qml_resources.cpp: /opt/homebrew/share/qt/libexec/rcc
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Running rcc for resource qml_resources"
	/opt/homebrew/share/qt/libexec/rcc --output /Users/<USER>/X/soloholic/ChamberUI3/build/.qt/rcc/qrc_qml_resources.cpp --name qml_resources /Users/<USER>/X/soloholic/ChamberUI3/build/.qt/rcc/qml_resources.qrc

ChamberUI_autogen/timestamp: /opt/homebrew/share/qt/libexec/moc
ChamberUI_autogen/timestamp: /opt/homebrew/share/qt/libexec/uic
ChamberUI_autogen/timestamp: CMakeFiles/ChamberUI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Automatic MOC and UIC for target ChamberUI"
	/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -E cmake_autogen /Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles/ChamberUI_autogen.dir/AutogenInfo.json ""
	/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -E touch /Users/<USER>/X/soloholic/ChamberUI3/build/ChamberUI_autogen/timestamp

ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp: /Users/<USER>/X/soloholic/ChamberUI3/qml.qrc
ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp: CMakeFiles/ChamberUI_autogen.dir/AutoRcc_qml_EWIEGA46WW_Info.json
ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp: /Users/<USER>/X/soloholic/ChamberUI3/asserts/file.svg
ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp: /Users/<USER>/X/soloholic/ChamberUI3/asserts/audio.svg
ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp: /Users/<USER>/X/soloholic/ChamberUI3/asserts/image.svg
ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp: /Users/<USER>/X/soloholic/ChamberUI3/asserts/attachment.svg
ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp: /Users/<USER>/X/soloholic/ChamberUI3/qml/main.qml
ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp: /Users/<USER>/X/soloholic/ChamberUI3/qml/components/MessageBubble.qml
ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp: /Users/<USER>/X/soloholic/ChamberUI3/qml/components/SessionList.qml
ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp: /Users/<USER>/X/soloholic/ChamberUI3/qml/components/InputArea.qml
ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp: /Users/<USER>/X/soloholic/ChamberUI3/qml/components/ChatView.qml
ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp: /Users/<USER>/X/soloholic/ChamberUI3/qml/styles/Colors.qml
ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp: /Users/<USER>/X/soloholic/ChamberUI3/qml/styles/Typography.qml
ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp: /Users/<USER>/X/soloholic/ChamberUI3/qml/styles/qmldir
ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp: /opt/homebrew/share/qt/libexec/rcc
ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp: /opt/homebrew/share/qt/libexec/rcc
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Automatic RCC for qml.qrc"
	/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -E cmake_autorcc /Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles/ChamberUI_autogen.dir/AutoRcc_qml_EWIEGA46WW_Info.json 

CMakeFiles/ChamberUI.dir/codegen:
.PHONY : CMakeFiles/ChamberUI.dir/codegen

CMakeFiles/ChamberUI.dir/ChamberUI_autogen/mocs_compilation.cpp.o: CMakeFiles/ChamberUI.dir/flags.make
CMakeFiles/ChamberUI.dir/ChamberUI_autogen/mocs_compilation.cpp.o: ChamberUI_autogen/mocs_compilation.cpp
CMakeFiles/ChamberUI.dir/ChamberUI_autogen/mocs_compilation.cpp.o: CMakeFiles/ChamberUI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/ChamberUI.dir/ChamberUI_autogen/mocs_compilation.cpp.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/ChamberUI.dir/ChamberUI_autogen/mocs_compilation.cpp.o -MF CMakeFiles/ChamberUI.dir/ChamberUI_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/ChamberUI.dir/ChamberUI_autogen/mocs_compilation.cpp.o -c /Users/<USER>/X/soloholic/ChamberUI3/build/ChamberUI_autogen/mocs_compilation.cpp

CMakeFiles/ChamberUI.dir/ChamberUI_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ChamberUI.dir/ChamberUI_autogen/mocs_compilation.cpp.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/X/soloholic/ChamberUI3/build/ChamberUI_autogen/mocs_compilation.cpp > CMakeFiles/ChamberUI.dir/ChamberUI_autogen/mocs_compilation.cpp.i

CMakeFiles/ChamberUI.dir/ChamberUI_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ChamberUI.dir/ChamberUI_autogen/mocs_compilation.cpp.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/X/soloholic/ChamberUI3/build/ChamberUI_autogen/mocs_compilation.cpp -o CMakeFiles/ChamberUI.dir/ChamberUI_autogen/mocs_compilation.cpp.s

CMakeFiles/ChamberUI.dir/src/main.cc.o: CMakeFiles/ChamberUI.dir/flags.make
CMakeFiles/ChamberUI.dir/src/main.cc.o: /Users/<USER>/X/soloholic/ChamberUI3/src/main.cc
CMakeFiles/ChamberUI.dir/src/main.cc.o: CMakeFiles/ChamberUI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/ChamberUI.dir/src/main.cc.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/ChamberUI.dir/src/main.cc.o -MF CMakeFiles/ChamberUI.dir/src/main.cc.o.d -o CMakeFiles/ChamberUI.dir/src/main.cc.o -c /Users/<USER>/X/soloholic/ChamberUI3/src/main.cc

CMakeFiles/ChamberUI.dir/src/main.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ChamberUI.dir/src/main.cc.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/X/soloholic/ChamberUI3/src/main.cc > CMakeFiles/ChamberUI.dir/src/main.cc.i

CMakeFiles/ChamberUI.dir/src/main.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ChamberUI.dir/src/main.cc.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/X/soloholic/ChamberUI3/src/main.cc -o CMakeFiles/ChamberUI.dir/src/main.cc.s

CMakeFiles/ChamberUI.dir/src/models/ChatMessage.cpp.o: CMakeFiles/ChamberUI.dir/flags.make
CMakeFiles/ChamberUI.dir/src/models/ChatMessage.cpp.o: /Users/<USER>/X/soloholic/ChamberUI3/src/models/ChatMessage.cpp
CMakeFiles/ChamberUI.dir/src/models/ChatMessage.cpp.o: CMakeFiles/ChamberUI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/ChamberUI.dir/src/models/ChatMessage.cpp.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/ChamberUI.dir/src/models/ChatMessage.cpp.o -MF CMakeFiles/ChamberUI.dir/src/models/ChatMessage.cpp.o.d -o CMakeFiles/ChamberUI.dir/src/models/ChatMessage.cpp.o -c /Users/<USER>/X/soloholic/ChamberUI3/src/models/ChatMessage.cpp

CMakeFiles/ChamberUI.dir/src/models/ChatMessage.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ChamberUI.dir/src/models/ChatMessage.cpp.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/X/soloholic/ChamberUI3/src/models/ChatMessage.cpp > CMakeFiles/ChamberUI.dir/src/models/ChatMessage.cpp.i

CMakeFiles/ChamberUI.dir/src/models/ChatMessage.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ChamberUI.dir/src/models/ChatMessage.cpp.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/X/soloholic/ChamberUI3/src/models/ChatMessage.cpp -o CMakeFiles/ChamberUI.dir/src/models/ChatMessage.cpp.s

CMakeFiles/ChamberUI.dir/src/models/ChatSession.cpp.o: CMakeFiles/ChamberUI.dir/flags.make
CMakeFiles/ChamberUI.dir/src/models/ChatSession.cpp.o: /Users/<USER>/X/soloholic/ChamberUI3/src/models/ChatSession.cpp
CMakeFiles/ChamberUI.dir/src/models/ChatSession.cpp.o: CMakeFiles/ChamberUI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/ChamberUI.dir/src/models/ChatSession.cpp.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/ChamberUI.dir/src/models/ChatSession.cpp.o -MF CMakeFiles/ChamberUI.dir/src/models/ChatSession.cpp.o.d -o CMakeFiles/ChamberUI.dir/src/models/ChatSession.cpp.o -c /Users/<USER>/X/soloholic/ChamberUI3/src/models/ChatSession.cpp

CMakeFiles/ChamberUI.dir/src/models/ChatSession.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ChamberUI.dir/src/models/ChatSession.cpp.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/X/soloholic/ChamberUI3/src/models/ChatSession.cpp > CMakeFiles/ChamberUI.dir/src/models/ChatSession.cpp.i

CMakeFiles/ChamberUI.dir/src/models/ChatSession.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ChamberUI.dir/src/models/ChatSession.cpp.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/X/soloholic/ChamberUI3/src/models/ChatSession.cpp -o CMakeFiles/ChamberUI.dir/src/models/ChatSession.cpp.s

CMakeFiles/ChamberUI.dir/src/viewmodels/ChatViewModel.cpp.o: CMakeFiles/ChamberUI.dir/flags.make
CMakeFiles/ChamberUI.dir/src/viewmodels/ChatViewModel.cpp.o: /Users/<USER>/X/soloholic/ChamberUI3/src/viewmodels/ChatViewModel.cpp
CMakeFiles/ChamberUI.dir/src/viewmodels/ChatViewModel.cpp.o: CMakeFiles/ChamberUI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/ChamberUI.dir/src/viewmodels/ChatViewModel.cpp.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/ChamberUI.dir/src/viewmodels/ChatViewModel.cpp.o -MF CMakeFiles/ChamberUI.dir/src/viewmodels/ChatViewModel.cpp.o.d -o CMakeFiles/ChamberUI.dir/src/viewmodels/ChatViewModel.cpp.o -c /Users/<USER>/X/soloholic/ChamberUI3/src/viewmodels/ChatViewModel.cpp

CMakeFiles/ChamberUI.dir/src/viewmodels/ChatViewModel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ChamberUI.dir/src/viewmodels/ChatViewModel.cpp.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/X/soloholic/ChamberUI3/src/viewmodels/ChatViewModel.cpp > CMakeFiles/ChamberUI.dir/src/viewmodels/ChatViewModel.cpp.i

CMakeFiles/ChamberUI.dir/src/viewmodels/ChatViewModel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ChamberUI.dir/src/viewmodels/ChatViewModel.cpp.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/X/soloholic/ChamberUI3/src/viewmodels/ChatViewModel.cpp -o CMakeFiles/ChamberUI.dir/src/viewmodels/ChatViewModel.cpp.s

CMakeFiles/ChamberUI.dir/src/viewmodels/SessionListViewModel.cpp.o: CMakeFiles/ChamberUI.dir/flags.make
CMakeFiles/ChamberUI.dir/src/viewmodels/SessionListViewModel.cpp.o: /Users/<USER>/X/soloholic/ChamberUI3/src/viewmodels/SessionListViewModel.cpp
CMakeFiles/ChamberUI.dir/src/viewmodels/SessionListViewModel.cpp.o: CMakeFiles/ChamberUI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/ChamberUI.dir/src/viewmodels/SessionListViewModel.cpp.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/ChamberUI.dir/src/viewmodels/SessionListViewModel.cpp.o -MF CMakeFiles/ChamberUI.dir/src/viewmodels/SessionListViewModel.cpp.o.d -o CMakeFiles/ChamberUI.dir/src/viewmodels/SessionListViewModel.cpp.o -c /Users/<USER>/X/soloholic/ChamberUI3/src/viewmodels/SessionListViewModel.cpp

CMakeFiles/ChamberUI.dir/src/viewmodels/SessionListViewModel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ChamberUI.dir/src/viewmodels/SessionListViewModel.cpp.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/X/soloholic/ChamberUI3/src/viewmodels/SessionListViewModel.cpp > CMakeFiles/ChamberUI.dir/src/viewmodels/SessionListViewModel.cpp.i

CMakeFiles/ChamberUI.dir/src/viewmodels/SessionListViewModel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ChamberUI.dir/src/viewmodels/SessionListViewModel.cpp.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/X/soloholic/ChamberUI3/src/viewmodels/SessionListViewModel.cpp -o CMakeFiles/ChamberUI.dir/src/viewmodels/SessionListViewModel.cpp.s

CMakeFiles/ChamberUI.dir/build/.qt/rcc/qrc_qml_resources.cpp.o: CMakeFiles/ChamberUI.dir/flags.make
CMakeFiles/ChamberUI.dir/build/.qt/rcc/qrc_qml_resources.cpp.o: .qt/rcc/qrc_qml_resources.cpp
CMakeFiles/ChamberUI.dir/build/.qt/rcc/qrc_qml_resources.cpp.o: CMakeFiles/ChamberUI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/ChamberUI.dir/build/.qt/rcc/qrc_qml_resources.cpp.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/ChamberUI.dir/build/.qt/rcc/qrc_qml_resources.cpp.o -MF CMakeFiles/ChamberUI.dir/build/.qt/rcc/qrc_qml_resources.cpp.o.d -o CMakeFiles/ChamberUI.dir/build/.qt/rcc/qrc_qml_resources.cpp.o -c /Users/<USER>/X/soloholic/ChamberUI3/build/.qt/rcc/qrc_qml_resources.cpp

CMakeFiles/ChamberUI.dir/build/.qt/rcc/qrc_qml_resources.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ChamberUI.dir/build/.qt/rcc/qrc_qml_resources.cpp.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/X/soloholic/ChamberUI3/build/.qt/rcc/qrc_qml_resources.cpp > CMakeFiles/ChamberUI.dir/build/.qt/rcc/qrc_qml_resources.cpp.i

CMakeFiles/ChamberUI.dir/build/.qt/rcc/qrc_qml_resources.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ChamberUI.dir/build/.qt/rcc/qrc_qml_resources.cpp.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/X/soloholic/ChamberUI3/build/.qt/rcc/qrc_qml_resources.cpp -o CMakeFiles/ChamberUI.dir/build/.qt/rcc/qrc_qml_resources.cpp.s

CMakeFiles/ChamberUI.dir/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.o: CMakeFiles/ChamberUI.dir/flags.make
CMakeFiles/ChamberUI.dir/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.o: ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp
CMakeFiles/ChamberUI.dir/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.o: CMakeFiles/ChamberUI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/ChamberUI.dir/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/ChamberUI.dir/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.o -MF CMakeFiles/ChamberUI.dir/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.o.d -o CMakeFiles/ChamberUI.dir/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.o -c /Users/<USER>/X/soloholic/ChamberUI3/build/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp

CMakeFiles/ChamberUI.dir/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ChamberUI.dir/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/X/soloholic/ChamberUI3/build/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp > CMakeFiles/ChamberUI.dir/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.i

CMakeFiles/ChamberUI.dir/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ChamberUI.dir/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/X/soloholic/ChamberUI3/build/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp -o CMakeFiles/ChamberUI.dir/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.s

# Object files for target ChamberUI
ChamberUI_OBJECTS = \
"CMakeFiles/ChamberUI.dir/ChamberUI_autogen/mocs_compilation.cpp.o" \
"CMakeFiles/ChamberUI.dir/src/main.cc.o" \
"CMakeFiles/ChamberUI.dir/src/models/ChatMessage.cpp.o" \
"CMakeFiles/ChamberUI.dir/src/models/ChatSession.cpp.o" \
"CMakeFiles/ChamberUI.dir/src/viewmodels/ChatViewModel.cpp.o" \
"CMakeFiles/ChamberUI.dir/src/viewmodels/SessionListViewModel.cpp.o" \
"CMakeFiles/ChamberUI.dir/build/.qt/rcc/qrc_qml_resources.cpp.o" \
"CMakeFiles/ChamberUI.dir/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.o"

# External object files for target ChamberUI
ChamberUI_EXTERNAL_OBJECTS =

ChamberUI.app/Contents/MacOS/ChamberUI: CMakeFiles/ChamberUI.dir/ChamberUI_autogen/mocs_compilation.cpp.o
ChamberUI.app/Contents/MacOS/ChamberUI: CMakeFiles/ChamberUI.dir/src/main.cc.o
ChamberUI.app/Contents/MacOS/ChamberUI: CMakeFiles/ChamberUI.dir/src/models/ChatMessage.cpp.o
ChamberUI.app/Contents/MacOS/ChamberUI: CMakeFiles/ChamberUI.dir/src/models/ChatSession.cpp.o
ChamberUI.app/Contents/MacOS/ChamberUI: CMakeFiles/ChamberUI.dir/src/viewmodels/ChatViewModel.cpp.o
ChamberUI.app/Contents/MacOS/ChamberUI: CMakeFiles/ChamberUI.dir/src/viewmodels/SessionListViewModel.cpp.o
ChamberUI.app/Contents/MacOS/ChamberUI: CMakeFiles/ChamberUI.dir/build/.qt/rcc/qrc_qml_resources.cpp.o
ChamberUI.app/Contents/MacOS/ChamberUI: CMakeFiles/ChamberUI.dir/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.o
ChamberUI.app/Contents/MacOS/ChamberUI: CMakeFiles/ChamberUI.dir/build.make
ChamberUI.app/Contents/MacOS/ChamberUI: /opt/homebrew/lib/QtQuick.framework/Versions/A/QtQuick
ChamberUI.app/Contents/MacOS/ChamberUI: /opt/homebrew/lib/QtQmlMeta.framework/Versions/A/QtQmlMeta
ChamberUI.app/Contents/MacOS/ChamberUI: /opt/homebrew/lib/QtQmlWorkerScript.framework/Versions/A/QtQmlWorkerScript
ChamberUI.app/Contents/MacOS/ChamberUI: /opt/homebrew/lib/QtQmlModels.framework/Versions/A/QtQmlModels
ChamberUI.app/Contents/MacOS/ChamberUI: /opt/homebrew/lib/QtQml.framework/Versions/A/QtQml
ChamberUI.app/Contents/MacOS/ChamberUI: /opt/homebrew/lib/QtOpenGL.framework/Versions/A/QtOpenGL
ChamberUI.app/Contents/MacOS/ChamberUI: /opt/homebrew/lib/QtGui.framework/Versions/A/QtGui
ChamberUI.app/Contents/MacOS/ChamberUI: /opt/homebrew/lib/QtNetwork.framework/Versions/A/QtNetwork
ChamberUI.app/Contents/MacOS/ChamberUI: /opt/homebrew/lib/QtCore.framework/Versions/A/QtCore
ChamberUI.app/Contents/MacOS/ChamberUI: CMakeFiles/ChamberUI.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Linking CXX executable ChamberUI.app/Contents/MacOS/ChamberUI"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/ChamberUI.dir/link.txt --verbose=$(VERBOSE)
	/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -E copy_directory /Users/<USER>/X/soloholic/ChamberUI3/qml /Users/<USER>/X/soloholic/ChamberUI3/build/ChamberUI.app/Contents/MacOS/../Resources/qml

# Rule to build all files generated by this target.
CMakeFiles/ChamberUI.dir/build: ChamberUI.app/Contents/MacOS/ChamberUI
.PHONY : CMakeFiles/ChamberUI.dir/build

CMakeFiles/ChamberUI.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/ChamberUI.dir/cmake_clean.cmake
.PHONY : CMakeFiles/ChamberUI.dir/clean

CMakeFiles/ChamberUI.dir/depend: .qt/rcc/qrc_qml_resources.cpp
CMakeFiles/ChamberUI.dir/depend: ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp
CMakeFiles/ChamberUI.dir/depend: ChamberUI_autogen/timestamp
	cd /Users/<USER>/X/soloholic/ChamberUI3/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/X/soloholic/ChamberUI3 /Users/<USER>/X/soloholic/ChamberUI3 /Users/<USER>/X/soloholic/ChamberUI3/build /Users/<USER>/X/soloholic/ChamberUI3/build /Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles/ChamberUI.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/ChamberUI.dir/depend

