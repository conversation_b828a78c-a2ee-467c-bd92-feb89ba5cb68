file(REMOVE_RECURSE
  ".qt/rcc/qrc_qml_resources.cpp"
  "CMakeFiles/ChamberUI_autogen.dir/AutogenUsed.txt"
  "CMakeFiles/ChamberUI_autogen.dir/ParseCache.txt"
  "ChamberUI_autogen"
  "CMakeFiles/ChamberUI.dir/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.o"
  "CMakeFiles/ChamberUI.dir/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.o.d"
  "CMakeFiles/ChamberUI.dir/ChamberUI_autogen/mocs_compilation.cpp.o"
  "CMakeFiles/ChamberUI.dir/ChamberUI_autogen/mocs_compilation.cpp.o.d"
  "CMakeFiles/ChamberUI.dir/build/.qt/rcc/qrc_qml_resources.cpp.o"
  "CMakeFiles/ChamberUI.dir/build/.qt/rcc/qrc_qml_resources.cpp.o.d"
  "CMakeFiles/ChamberUI.dir/src/main.cc.o"
  "CMakeFiles/ChamberUI.dir/src/main.cc.o.d"
  "CMakeFiles/ChamberUI.dir/src/models/ChatMessage.cpp.o"
  "CMakeFiles/ChamberUI.dir/src/models/ChatMessage.cpp.o.d"
  "CMakeFiles/ChamberUI.dir/src/models/ChatSession.cpp.o"
  "CMakeFiles/ChamberUI.dir/src/models/ChatSession.cpp.o.d"
  "CMakeFiles/ChamberUI.dir/src/viewmodels/ChatViewModel.cpp.o"
  "CMakeFiles/ChamberUI.dir/src/viewmodels/ChatViewModel.cpp.o.d"
  "CMakeFiles/ChamberUI.dir/src/viewmodels/SessionListViewModel.cpp.o"
  "CMakeFiles/ChamberUI.dir/src/viewmodels/SessionListViewModel.cpp.o.d"
  "ChamberUI.app/Contents/MacOS/ChamberUI"
  "ChamberUI.pdb"
  "ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp"
  "ChamberUI_autogen/mocs_compilation.cpp"
  "ChamberUI_autogen/timestamp"
)

# Per-language clean rules from dependency scanning.
foreach(lang CXX)
  include(CMakeFiles/ChamberUI.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
