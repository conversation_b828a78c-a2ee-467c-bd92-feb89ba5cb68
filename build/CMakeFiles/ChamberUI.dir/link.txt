/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/ChamberUI.dir/ChamberUI_autogen/mocs_compilation.cpp.o CMakeFiles/ChamberUI.dir/src/main.cc.o CMakeFiles/ChamberUI.dir/src/models/ChatMessage.cpp.o CMakeFiles/ChamberUI.dir/src/models/ChatSession.cpp.o CMakeFiles/ChamberUI.dir/src/viewmodels/ChatViewModel.cpp.o CMakeFiles/ChamberUI.dir/src/viewmodels/SessionListViewModel.cpp.o CMakeFiles/ChamberUI.dir/build/.qt/rcc/qrc_qml_resources.cpp.o CMakeFiles/ChamberUI.dir/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.o -o ChamberUI.app/Contents/MacOS/ChamberUI -F/opt/homebrew/lib  -Wl,-rpath,/opt/homebrew/lib /opt/homebrew/lib/QtQuick.framework/Versions/A/QtQuick /opt/homebrew/lib/QtQmlMeta.framework/Versions/A/QtQmlMeta /opt/homebrew/lib/QtQmlWorkerScript.framework/Versions/A/QtQmlWorkerScript /opt/homebrew/lib/QtQmlModels.framework/Versions/A/QtQmlModels /opt/homebrew/lib/QtQml.framework/Versions/A/QtQml /opt/homebrew/lib/QtOpenGL.framework/Versions/A/QtOpenGL /opt/homebrew/lib/QtGui.framework/Versions/A/QtGui -framework AGL -framework AppKit -framework OpenGL -framework ImageIO -framework Metal /opt/homebrew/lib/QtNetwork.framework/Versions/A/QtNetwork /opt/homebrew/lib/QtCore.framework/Versions/A/QtCore -framework IOKit -framework DiskArbitration -framework UniformTypeIdentifiers
