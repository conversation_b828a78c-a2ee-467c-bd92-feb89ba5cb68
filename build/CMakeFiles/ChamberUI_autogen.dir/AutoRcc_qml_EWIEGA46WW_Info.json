{"BUILD_DIR": "/Users/<USER>/X/soloholic/ChamberUI3/build/ChamberUI_autogen", "CMAKE_BINARY_DIR": "/Users/<USER>/X/soloholic/ChamberUI3/build", "CMAKE_CURRENT_BINARY_DIR": "/Users/<USER>/X/soloholic/ChamberUI3/build", "CMAKE_CURRENT_SOURCE_DIR": "/Users/<USER>/X/soloholic/ChamberUI3", "CMAKE_SOURCE_DIR": "/Users/<USER>/X/soloholic/ChamberUI3", "CROSS_CONFIG": false, "GENERATOR": "Unix Makefiles", "INCLUDE_DIR": "/Users/<USER>/X/soloholic/ChamberUI3/build/ChamberUI_autogen/include", "INPUTS": ["/Users/<USER>/X/soloholic/ChamberUI3/asserts/file.svg", "/Users/<USER>/X/soloholic/ChamberUI3/asserts/audio.svg", "/Users/<USER>/X/soloholic/ChamberUI3/asserts/image.svg", "/Users/<USER>/X/soloholic/ChamberUI3/asserts/attachment.svg", "/Users/<USER>/X/soloholic/ChamberUI3/qml/main.qml", "/Users/<USER>/X/soloholic/ChamberUI3/qml/components/MessageBubble.qml", "/Users/<USER>/X/soloholic/ChamberUI3/qml/components/SessionList.qml", "/Users/<USER>/X/soloholic/ChamberUI3/qml/components/InputArea.qml", "/Users/<USER>/X/soloholic/ChamberUI3/qml/components/ChatView.qml", "/Users/<USER>/X/soloholic/ChamberUI3/qml/styles/Colors.qml", "/Users/<USER>/X/soloholic/ChamberUI3/qml/styles/Typography.qml", "/Users/<USER>/X/soloholic/ChamberUI3/qml/styles/qmldir"], "LOCK_FILE": "/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles/ChamberUI_autogen.dir/AutoRcc_qml_EWIEGA46WW_Lock.lock", "MULTI_CONFIG": false, "OPTIONS": ["-name", "qml"], "OUTPUT_CHECKSUM": "EWIEGA46WW", "OUTPUT_NAME": "qrc_qml.cpp", "RCC_EXECUTABLE": "/opt/homebrew/share/qt/libexec/rcc", "RCC_LIST_OPTIONS": ["--list"], "SETTINGS_FILE": "/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles/ChamberUI_autogen.dir/AutoRcc_qml_EWIEGA46WW_Used.txt", "SOURCE": "/Users/<USER>/X/soloholic/ChamberUI3/qml.qrc", "USE_BETTER_GRAPH": true, "VERBOSITY": 0}