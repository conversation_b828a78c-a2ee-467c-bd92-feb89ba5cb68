# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake

# The command to remove a file.
RM = /Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/X/soloholic/ChamberUI3

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/X/soloholic/ChamberUI3/build

# Utility rule file for ChamberUI_qmlimportscan.

# Include any custom commands dependencies for this target.
include CMakeFiles/ChamberUI_qmlimportscan.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/ChamberUI_qmlimportscan.dir/progress.make

CMakeFiles/ChamberUI_qmlimportscan: .qt/qml_imports/ChamberUI_build.cmake

.qt/qml_imports/ChamberUI_build.cmake: /opt/homebrew/share/qt/libexec/qmlimportscanner
.qt/qml_imports/ChamberUI_build.cmake: .qt/rcc/qml_resources.qrc
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Running qmlimportscanner for ChamberUI"
	cd /Users/<USER>/X/soloholic/ChamberUI3 && /opt/homebrew/share/qt/libexec/qmlimportscanner @/Users/<USER>/X/soloholic/ChamberUI3/build/.qt/qml_imports/ChamberUI_build.rsp

CMakeFiles/ChamberUI_qmlimportscan.dir/codegen:
.PHONY : CMakeFiles/ChamberUI_qmlimportscan.dir/codegen

ChamberUI_qmlimportscan: .qt/qml_imports/ChamberUI_build.cmake
ChamberUI_qmlimportscan: CMakeFiles/ChamberUI_qmlimportscan
ChamberUI_qmlimportscan: CMakeFiles/ChamberUI_qmlimportscan.dir/build.make
.PHONY : ChamberUI_qmlimportscan

# Rule to build all files generated by this target.
CMakeFiles/ChamberUI_qmlimportscan.dir/build: ChamberUI_qmlimportscan
.PHONY : CMakeFiles/ChamberUI_qmlimportscan.dir/build

CMakeFiles/ChamberUI_qmlimportscan.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/ChamberUI_qmlimportscan.dir/cmake_clean.cmake
.PHONY : CMakeFiles/ChamberUI_qmlimportscan.dir/clean

CMakeFiles/ChamberUI_qmlimportscan.dir/depend:
	cd /Users/<USER>/X/soloholic/ChamberUI3/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/X/soloholic/ChamberUI3 /Users/<USER>/X/soloholic/ChamberUI3 /Users/<USER>/X/soloholic/ChamberUI3/build /Users/<USER>/X/soloholic/ChamberUI3/build /Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles/ChamberUI_qmlimportscan.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/ChamberUI_qmlimportscan.dir/depend

