# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake

# The command to remove a file.
RM = /Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/X/soloholic/ChamberUI3

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/X/soloholic/ChamberUI3/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/ChamberUI.dir/all
all: _deps/gsl-build/all
all: _deps/magic_enum-build/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/ChamberUI.dir/codegen
codegen: _deps/gsl-build/codegen
codegen: _deps/magic_enum-build/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall: _deps/gsl-build/preinstall
preinstall: _deps/magic_enum-build/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/ChamberUI.dir/clean
clean: CMakeFiles/ChamberUI_qmlimportscan.dir/clean
clean: CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/clean
clean: CMakeFiles/ChamberUI_autogen.dir/clean
clean: _deps/gsl-build/clean
clean: _deps/magic_enum-build/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory _deps/gsl-build

# Recursive "all" directory target.
_deps/gsl-build/all: _deps/gsl-build/include/all
.PHONY : _deps/gsl-build/all

# Recursive "codegen" directory target.
_deps/gsl-build/codegen: _deps/gsl-build/include/codegen
.PHONY : _deps/gsl-build/codegen

# Recursive "preinstall" directory target.
_deps/gsl-build/preinstall: _deps/gsl-build/include/preinstall
.PHONY : _deps/gsl-build/preinstall

# Recursive "clean" directory target.
_deps/gsl-build/clean: _deps/gsl-build/include/clean
.PHONY : _deps/gsl-build/clean

#=============================================================================
# Directory level rules for directory _deps/gsl-build/include

# Recursive "all" directory target.
_deps/gsl-build/include/all:
.PHONY : _deps/gsl-build/include/all

# Recursive "codegen" directory target.
_deps/gsl-build/include/codegen:
.PHONY : _deps/gsl-build/include/codegen

# Recursive "preinstall" directory target.
_deps/gsl-build/include/preinstall:
.PHONY : _deps/gsl-build/include/preinstall

# Recursive "clean" directory target.
_deps/gsl-build/include/clean:
.PHONY : _deps/gsl-build/include/clean

#=============================================================================
# Directory level rules for directory _deps/magic_enum-build

# Recursive "all" directory target.
_deps/magic_enum-build/all:
.PHONY : _deps/magic_enum-build/all

# Recursive "codegen" directory target.
_deps/magic_enum-build/codegen:
.PHONY : _deps/magic_enum-build/codegen

# Recursive "preinstall" directory target.
_deps/magic_enum-build/preinstall:
.PHONY : _deps/magic_enum-build/preinstall

# Recursive "clean" directory target.
_deps/magic_enum-build/clean:
.PHONY : _deps/magic_enum-build/clean

#=============================================================================
# Target rules for target CMakeFiles/ChamberUI.dir

# All Build rule for target.
CMakeFiles/ChamberUI.dir/all: CMakeFiles/ChamberUI_autogen.dir/all
CMakeFiles/ChamberUI.dir/all: CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/all
CMakeFiles/ChamberUI.dir/all: CMakeFiles/ChamberUI_qmlimportscan.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12 "Built target ChamberUI"
.PHONY : CMakeFiles/ChamberUI.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ChamberUI.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles 14
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ChamberUI.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles 0
.PHONY : CMakeFiles/ChamberUI.dir/rule

# Convenience name for target.
ChamberUI: CMakeFiles/ChamberUI.dir/rule
.PHONY : ChamberUI

# codegen rule for target.
CMakeFiles/ChamberUI.dir/codegen: CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/all
CMakeFiles/ChamberUI.dir/codegen: CMakeFiles/ChamberUI_qmlimportscan.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12 "Finished codegen for target ChamberUI"
.PHONY : CMakeFiles/ChamberUI.dir/codegen

# clean rule for target.
CMakeFiles/ChamberUI.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/clean
.PHONY : CMakeFiles/ChamberUI.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ChamberUI_qmlimportscan.dir

# All Build rule for target.
CMakeFiles/ChamberUI_qmlimportscan.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI_qmlimportscan.dir/build.make CMakeFiles/ChamberUI_qmlimportscan.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI_qmlimportscan.dir/build.make CMakeFiles/ChamberUI_qmlimportscan.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles --progress-num=14 "Built target ChamberUI_qmlimportscan"
.PHONY : CMakeFiles/ChamberUI_qmlimportscan.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ChamberUI_qmlimportscan.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ChamberUI_qmlimportscan.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles 0
.PHONY : CMakeFiles/ChamberUI_qmlimportscan.dir/rule

# Convenience name for target.
ChamberUI_qmlimportscan: CMakeFiles/ChamberUI_qmlimportscan.dir/rule
.PHONY : ChamberUI_qmlimportscan

# codegen rule for target.
CMakeFiles/ChamberUI_qmlimportscan.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI_qmlimportscan.dir/build.make CMakeFiles/ChamberUI_qmlimportscan.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles --progress-num=14 "Finished codegen for target ChamberUI_qmlimportscan"
.PHONY : CMakeFiles/ChamberUI_qmlimportscan.dir/codegen

# clean rule for target.
CMakeFiles/ChamberUI_qmlimportscan.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI_qmlimportscan.dir/build.make CMakeFiles/ChamberUI_qmlimportscan.dir/clean
.PHONY : CMakeFiles/ChamberUI_qmlimportscan.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ChamberUI_autogen_timestamp_deps.dir

# All Build rule for target.
CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/all: CMakeFiles/ChamberUI_qmlimportscan.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/build.make CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/build.make CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles --progress-num= "Built target ChamberUI_autogen_timestamp_deps"
.PHONY : CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles 0
.PHONY : CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/rule

# Convenience name for target.
ChamberUI_autogen_timestamp_deps: CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/rule
.PHONY : ChamberUI_autogen_timestamp_deps

# codegen rule for target.
CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/build.make CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles --progress-num= "Finished codegen for target ChamberUI_autogen_timestamp_deps"
.PHONY : CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/codegen

# clean rule for target.
CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/build.make CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/clean
.PHONY : CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ChamberUI_autogen.dir

# All Build rule for target.
CMakeFiles/ChamberUI_autogen.dir/all: CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI_autogen.dir/build.make CMakeFiles/ChamberUI_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI_autogen.dir/build.make CMakeFiles/ChamberUI_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles --progress-num=13 "Built target ChamberUI_autogen"
.PHONY : CMakeFiles/ChamberUI_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ChamberUI_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ChamberUI_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles 0
.PHONY : CMakeFiles/ChamberUI_autogen.dir/rule

# Convenience name for target.
ChamberUI_autogen: CMakeFiles/ChamberUI_autogen.dir/rule
.PHONY : ChamberUI_autogen

# codegen rule for target.
CMakeFiles/ChamberUI_autogen.dir/codegen: CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI_autogen.dir/build.make CMakeFiles/ChamberUI_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles --progress-num=13 "Finished codegen for target ChamberUI_autogen"
.PHONY : CMakeFiles/ChamberUI_autogen.dir/codegen

# clean rule for target.
CMakeFiles/ChamberUI_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI_autogen.dir/build.make CMakeFiles/ChamberUI_autogen.dir/clean
.PHONY : CMakeFiles/ChamberUI_autogen.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

