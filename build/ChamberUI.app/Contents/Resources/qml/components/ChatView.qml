import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../styles"

Rectangle {
    id: root
    
    property var currentSession: null
    property alias inputText: inputArea.text
    property bool isTyping: false
    property bool canSend: false

    signal sendMessage(string message)
    signal chatInputTextChanged()
    
    color: Colors.chatBackground
    
    ColumnLayout {
        anchors.fill: parent
        spacing: 0
        
        // Header
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 60
            color: Colors.surfaceBackground
            
            Rectangle {
                anchors.bottom: parent.bottom
                width: parent.width
                height: 1
                color: Colors.divider
            }
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 16
                
                Text {
                    Layout.fillWidth: true
                    text: root.currentSession ? root.currentSession.title : "ChamberUI Assistant"
                    color: Colors.primaryText
                    font.family: Typography.primaryFont
                    font.pixelSize: Typography.titleSmall
                    font.weight: Typography.semiBold
                    elide: Text.ElideRight
                }
                
                // Status indicator
                Row {
                    spacing: 8
                    visible: root.isTyping
                    
                    Text {
                        text: "AI is typing"
                        color: Colors.secondaryText
                        font.family: Typography.primaryFont
                        font.pixelSize: Typography.bodySmall
                        anchors.verticalCenter: parent.verticalCenter
                    }
                    
                    // Typing animation
                    Row {
                        spacing: 4
                        anchors.verticalCenter: parent.verticalCenter
                        
                        Repeater {
                            model: 3
                            Rectangle {
                                width: 4
                                height: 4
                                radius: 2
                                color: Colors.primary
                                
                                SequentialAnimation on opacity {
                                    running: root.isTyping
                                    loops: Animation.Infinite
                                    
                                    PauseAnimation { duration: index * 200 }
                                    NumberAnimation { to: 0.3; duration: 400 }
                                    NumberAnimation { to: 1.0; duration: 400 }
                                    PauseAnimation { duration: (2 - index) * 200 }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // Messages area
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "transparent"

            ScrollView {
                id: scrollView
                anchors.fill: parent
                visible: root.currentSession && root.currentSession.messageCount > 0

                ScrollBar.horizontal.policy: ScrollBar.AlwaysOff
                ScrollBar.vertical.policy: ScrollBar.AsNeeded

                ListView {
                    id: messagesListView

                    anchors.fill: parent
                    anchors.margins: 8

                    model: root.currentSession ? root.currentSession.messages : null
                    spacing: 12

                    // Add padding at top and bottom
                    topMargin: 16
                    bottomMargin: 16

                    delegate: MessageBubble {
                        width: messagesListView.width
                        message: model.content || ""
                        isUser: model.isUser || false
                        timestamp: {
                            if (model.timestamp) {
                                return Qt.formatDateTime(model.timestamp, "hh:mm")
                            }
                            return ""
                        }
                        messageStatus: model.status || 0
                    }

                    // Auto-scroll to bottom when new messages arrive
                    onCountChanged: {
                        Qt.callLater(function() {
                            if (messagesListView.count > 0) {
                                messagesListView.positionViewAtEnd()
                            }
                        })
                    }
                }
            }

            // Empty state
            Item {
                anchors.centerIn: parent
                visible: !root.currentSession || (root.currentSession && root.currentSession.messageCount === 0)

                Column {
                    anchors.centerIn: parent
                    spacing: 16

                    Text {
                        text: "💬"
                        font.pixelSize: 48
                        horizontalAlignment: Text.AlignHCenter
                        anchors.horizontalCenter: parent.horizontalCenter
                    }

                    Text {
                        text: "Start a conversation"
                        color: Colors.secondaryText
                        font.family: Typography.primaryFont
                        font.pixelSize: Typography.titleSmall
                        font.weight: Typography.medium
                        horizontalAlignment: Text.AlignHCenter
                        anchors.horizontalCenter: parent.horizontalCenter
                    }

                    Text {
                        text: "Ask me anything, and I'll do my best to help!"
                        color: Colors.mutedText
                        font.family: Typography.primaryFont
                        font.pixelSize: Typography.bodyMedium
                        horizontalAlignment: Text.AlignHCenter
                        anchors.horizontalCenter: parent.horizontalCenter
                    }
                }
            }
        }
        
        // Input area
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: Math.max(80, inputArea.height + 32)
            Layout.minimumHeight: 80
            color: Colors.surfaceBackground
            
            Rectangle {
                anchors.top: parent.top
                width: parent.width
                height: 1
                color: Colors.divider
            }
            
            InputArea {
                id: inputArea
                anchors.left: parent.left
                anchors.right: parent.right
                anchors.verticalCenter: parent.verticalCenter
                anchors.margins: 16
                
                canSend: root.canSend
                
                onSendRequested: {
                    if (text.trim().length > 0) {
                        root.sendMessage(text.trim())
                        text = ""
                    }
                }
                
                onInputTextChanged: root.chatInputTextChanged()
            }
        }
    }
}
