import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../styles"

Item {
    id: root
    
    property alias message: messageText.text
    property bool isUser: false
    property string timestamp: ""
    property int messageStatus: 0 // 0: Sending, 1: <PERSON>t, 2: Received, 3: Error
    
    width: parent ? parent.width : 400
    height: messageContainer.height + 20

    TextMetrics {
        id: textMetrics
        font.family: Typography.primaryFont
        font.pixelSize: Typography.bodyMedium
        text: root.message
        elideWidth: Math.min(400, parent.width * 0.75)
    }
    
    Rectangle {
        id: messageContainer

        width: Math.min(parent.width * 0.75, Math.max(200, textMetrics.boundingRect.width + 40))
        height: Math.max(48, textMetrics.boundingRect.height + 32)

        anchors.right: root.isUser ? parent.right : undefined
        anchors.left: root.isUser ? undefined : parent.left
        anchors.rightMargin: root.isUser ? 20 : 0
        anchors.leftMargin: root.isUser ? 0 : 20
        
        color: root.isUser ? Colors.userMessageBackground : Colors.aiMessageBackground
        radius: 16
        
        // Add subtle shadow effect
        Rectangle {
            anchors.fill: parent
            anchors.topMargin: 2
            color: Qt.rgba(0, 0, 0, 0.1)
            radius: parent.radius
            z: -1
        }
        
        Text {
            id: messageText

            anchors.fill: parent
            anchors.margins: 16

            text: root.message
            color: root.isUser ? Colors.userMessageText : Colors.aiMessageText
            font.family: Typography.primaryFont
            font.pixelSize: Typography.bodyMedium

            wrapMode: Text.Wrap
            textFormat: Text.PlainText
        }
        
        // Status indicator for user messages
        Rectangle {
            visible: root.isUser
            width: 8
            height: 8
            radius: 4
            
            anchors.bottom: parent.bottom
            anchors.right: parent.right
            anchors.margins: 8
            
            color: {
                switch (root.messageStatus) {
                    case 0: return Colors.warning  // Sending
                    case 1: return Colors.success  // Sent
                    case 2: return Colors.success  // Received
                    case 3: return Colors.error    // Error
                    default: return Colors.mutedText
                }
            }
            
            Behavior on color {
                ColorAnimation { duration: 200 }
            }
        }
    }
    
    // Timestamp
    Text {
        visible: root.timestamp.length > 0
        text: root.timestamp
        color: Colors.mutedText
        font.family: Typography.primaryFont
        font.pixelSize: Typography.captionSmall
        
        anchors.top: messageContainer.bottom
        anchors.topMargin: 4
        anchors.right: root.isUser ? messageContainer.right : undefined
        anchors.left: root.isUser ? undefined : messageContainer.left
    }
    
    // Hover effect
    MouseArea {
        anchors.fill: messageContainer
        hoverEnabled: true
        
        onEntered: {
            messageContainer.color = Qt.lighter(
                root.isUser ? Colors.userMessageBackground : Colors.aiMessageBackground, 
                1.1
            )
        }
        
        onExited: {
            messageContainer.color = root.isUser ? Colors.userMessageBackground : Colors.aiMessageBackground
        }
    }
}
