import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Window 2.15
import QtQuick.Layouts 1.15
import ChamberUI 1.0
import "components"
import "styles"

ApplicationWindow {
    id: window

    width: 1200
    height: 800
    minimumWidth: 800
    minimumHeight: 600

    visible: true
    title: "ChamberUI - LLM Chat Assistant"

    color: Colors.background

    // Create view models
    property var sessionListViewModel: SessionListViewModel {
        id: sessionListVM
        Component.onCompleted: sessionListVM.initializeWithSampleData()
    }

    property var chatViewModel: ChatViewModel {
        id: chatVM
        currentSession: sessionListVM.currentSession
    }

    // Main layout
    RowLayout {
        anchors.fill: parent
        spacing: 0

        // Left sidebar - Session list
        Rectangle {
            Layout.preferredWidth: 300
            Layout.fillHeight: true
            Layout.minimumWidth: 250
            Layout.maximumWidth: 350

            color: Colors.sidebarBackground

            // Resize handle
            Rectangle {
                anchors.right: parent.right
                anchors.top: parent.top
                anchors.bottom: parent.bottom
                width: 1
                color: Colors.divider
            }

            SessionList {
                anchors.fill: parent
                model: sessionListVM.sessions

                onSessionSelected: function(session) {
                    sessionListVM.currentSession = session
                }

                onNewChatRequested: {
                    var newSession = sessionListVM.createNewSession()
                    sessionListVM.currentSession = newSession
                }
            }
        }

        // Right side - Chat area
        ChatView {
            Layout.fillWidth: true
            Layout.fillHeight: true

            currentSession: sessionListVM.currentSession
            inputText: chatVM.inputText
            isTyping: chatVM.isTyping
            canSend: chatVM.canSend

            onSendMessage: function(message) {
                chatVM.sendMessage(message)
            }

            onChatInputTextChanged: {
                chatVM.setInputText(inputText)
            }
        }
    }

    // Window state management
    Component.onCompleted: {
        // Set initial window state
        console.log("ChamberUI initialized with", sessionListVM.sessionCount, "sessions")
    }
}