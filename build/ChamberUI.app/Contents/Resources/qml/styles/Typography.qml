pragma Singleton
import QtQuick 2.15

QtObject {
    // Font families
    readonly property string primaryFont: "Arial"
    readonly property string monoFont: "Courier"
    readonly property string fallbackFont: "Arial"
    
    // Font sizes
    readonly property int titleLarge: 24
    readonly property int titleMedium: 20
    readonly property int titleSmall: 16
    
    readonly property int bodyLarge: 16
    readonly property int bodyMedium: 14
    readonly property int bodySmall: 12
    
    readonly property int labelLarge: 14
    readonly property int labelMedium: 12
    readonly property int labelSmall: 10
    
    readonly property int captionLarge: 12
    readonly property int captionSmall: 10
    
    // Font weights
    readonly property int light: Font.Light
    readonly property int normal: Font.Normal
    readonly property int medium: Font.Medium
    readonly property int semiBold: Font.DemiBold
    readonly property int bold: Font.Bold
    
    // Line heights (as multipliers)
    readonly property real lineHeightTight: 1.2
    readonly property real lineHeightNormal: 1.4
    readonly property real lineHeightRelaxed: 1.6
    
    // Letter spacing
    readonly property real letterSpacingTight: -0.5
    readonly property real letterSpacingNormal: 0
    readonly property real letterSpacingWide: 0.5
}
