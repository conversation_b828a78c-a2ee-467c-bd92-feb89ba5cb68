/****************************************************************************
** Meta object code from reading C++ file 'ChatMessage.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../src/models/ChatMessage.h"
#include <QtCore/qmetatype.h>
#include <QtCore/QProperty>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ChatMessage.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN11ChatMessageE_t {};
} // unnamed namespace

template <> constexpr inline auto ChatMessage::qt_create_metaobjectdata<qt_meta_tag_ZN11ChatMessageE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ChatMessage",
        "QML.Element",
        "auto",
        "contentChanged",
        "",
        "isUserChanged",
        "timestampChanged",
        "statusChanged",
        "content",
        "isUser",
        "timestamp",
        "status",
        "MessageStatus",
        "Sending",
        "Sent",
        "Received",
        "Error"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'contentChanged'
        QtMocHelpers::SignalData<void()>(3, 4, QMC::AccessPublic, QMetaType::Void),
        // Signal 'isUserChanged'
        QtMocHelpers::SignalData<void()>(5, 4, QMC::AccessPublic, QMetaType::Void),
        // Signal 'timestampChanged'
        QtMocHelpers::SignalData<void()>(6, 4, QMC::AccessPublic, QMetaType::Void),
        // Signal 'statusChanged'
        QtMocHelpers::SignalData<void()>(7, 4, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'content'
        QtMocHelpers::PropertyData<QString>(8, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet | QMC::Bindable, 0),
        // property 'isUser'
        QtMocHelpers::PropertyData<bool>(9, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet | QMC::Bindable, 1),
        // property 'timestamp'
        QtMocHelpers::PropertyData<QDateTime>(10, QMetaType::QDateTime, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet | QMC::Bindable, 2),
        // property 'status'
        QtMocHelpers::PropertyData<MessageStatus>(11, 0x80000000 | 12, QMC::DefaultPropertyFlags | QMC::Writable | QMC::EnumOrFlag | QMC::StdCppSet | QMC::Bindable, 3),
    };
    QtMocHelpers::UintData qt_enums {
        // enum 'MessageStatus'
        QtMocHelpers::EnumData<MessageStatus>(12, 12, QMC::EnumIsScoped).add({
            {   13, MessageStatus::Sending },
            {   14, MessageStatus::Sent },
            {   15, MessageStatus::Received },
            {   16, MessageStatus::Error },
        }),
    };
    QtMocHelpers::UintData qt_constructors {};
    QtMocHelpers::ClassInfos qt_classinfo({
            {    1,    2 },
    });
    return QtMocHelpers::metaObjectData<ChatMessage, void>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums, qt_constructors, qt_classinfo);
}
Q_CONSTINIT const QMetaObject ChatMessage::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN11ChatMessageE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN11ChatMessageE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN11ChatMessageE_t>.metaTypes,
    nullptr
} };

void ChatMessage::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ChatMessage *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->contentChanged(); break;
        case 1: _t->isUserChanged(); break;
        case 2: _t->timestampChanged(); break;
        case 3: _t->statusChanged(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ChatMessage::*)()>(_a, &ChatMessage::contentChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ChatMessage::*)()>(_a, &ChatMessage::isUserChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ChatMessage::*)()>(_a, &ChatMessage::timestampChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ChatMessage::*)()>(_a, &ChatMessage::statusChanged, 3))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<QString*>(_v) = _t->content(); break;
        case 1: *reinterpret_cast<bool*>(_v) = _t->isUser(); break;
        case 2: *reinterpret_cast<QDateTime*>(_v) = _t->timestamp(); break;
        case 3: *reinterpret_cast<MessageStatus*>(_v) = _t->status(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setContent(*reinterpret_cast<QString*>(_v)); break;
        case 1: _t->setIsUser(*reinterpret_cast<bool*>(_v)); break;
        case 2: _t->setTimestamp(*reinterpret_cast<QDateTime*>(_v)); break;
        case 3: _t->setStatus(*reinterpret_cast<MessageStatus*>(_v)); break;
        default: break;
        }
    }
    if (_c == QMetaObject::BindableProperty) {
        switch (_id) {
        case 0: *static_cast<QUntypedBindable *>(_a[0]) = _t->bindableContent(); break;
        case 1: *static_cast<QUntypedBindable *>(_a[0]) = _t->bindableIsUser(); break;
        case 2: *static_cast<QUntypedBindable *>(_a[0]) = _t->bindableTimestamp(); break;
        case 3: *static_cast<QUntypedBindable *>(_a[0]) = _t->bindableStatus(); break;
        default: break;
        }
    }
}

const QMetaObject *ChatMessage::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ChatMessage::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN11ChatMessageE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ChatMessage::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 4;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void ChatMessage::contentChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ChatMessage::isUserChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ChatMessage::timestampChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ChatMessage::statusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}
QT_WARNING_POP
