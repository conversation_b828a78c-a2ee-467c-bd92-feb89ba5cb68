/****************************************************************************
** Meta object code from reading C++ file 'SessionListViewModel.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../src/viewmodels/SessionListViewModel.h"
#include <QtCore/qmetatype.h>
#include <QtCore/QProperty>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'SessionListViewModel.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN20SessionListViewModelE_t {};
} // unnamed namespace

template <> constexpr inline auto SessionListViewModel::qt_create_metaobjectdata<qt_meta_tag_ZN20SessionListViewModelE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "SessionListViewModel",
        "QML.Element",
        "auto",
        "sessionsChanged",
        "",
        "currentSessionChanged",
        "sessionCountChanged",
        "searchTextChanged",
        "sessionSelected",
        "ChatSession*",
        "session",
        "createNewSession",
        "title",
        "deleteSession",
        "index",
        "getSession",
        "selectSession",
        "getFilteredSessions",
        "QList<ChatSession*>",
        "initializeWithSampleData",
        "sessions",
        "QQmlListProperty<ChatSession>",
        "currentSession",
        "sessionCount",
        "searchText"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'sessionsChanged'
        QtMocHelpers::SignalData<void()>(3, 4, QMC::AccessPublic, QMetaType::Void),
        // Signal 'currentSessionChanged'
        QtMocHelpers::SignalData<void()>(5, 4, QMC::AccessPublic, QMetaType::Void),
        // Signal 'sessionCountChanged'
        QtMocHelpers::SignalData<void()>(6, 4, QMC::AccessPublic, QMetaType::Void),
        // Signal 'searchTextChanged'
        QtMocHelpers::SignalData<void()>(7, 4, QMC::AccessPublic, QMetaType::Void),
        // Signal 'sessionSelected'
        QtMocHelpers::SignalData<void(ChatSession *)>(8, 4, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 9, 10 },
        }}),
        // Method 'createNewSession'
        QtMocHelpers::MethodData<ChatSession *()>(11, 4, QMC::AccessPublic, 0x80000000 | 9),
        // Method 'createNewSession'
        QtMocHelpers::MethodData<ChatSession *(const QString &)>(11, 4, QMC::AccessPublic, 0x80000000 | 9, {{
            { QMetaType::QString, 12 },
        }}),
        // Method 'deleteSession'
        QtMocHelpers::MethodData<void(ChatSession *)>(13, 4, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 9, 10 },
        }}),
        // Method 'deleteSession'
        QtMocHelpers::MethodData<void(int)>(13, 4, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 14 },
        }}),
        // Method 'getSession'
        QtMocHelpers::MethodData<ChatSession *(int) const>(15, 4, QMC::AccessPublic, 0x80000000 | 9, {{
            { QMetaType::Int, 14 },
        }}),
        // Method 'selectSession'
        QtMocHelpers::MethodData<void(ChatSession *)>(16, 4, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 9, 10 },
        }}),
        // Method 'selectSession'
        QtMocHelpers::MethodData<void(int)>(16, 4, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 14 },
        }}),
        // Method 'getFilteredSessions'
        QtMocHelpers::MethodData<QList<ChatSession*>() const>(17, 4, QMC::AccessPublic, 0x80000000 | 18),
        // Method 'initializeWithSampleData'
        QtMocHelpers::MethodData<void()>(19, 4, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'sessions'
        QtMocHelpers::PropertyData<QQmlListProperty<ChatSession>>(20, 0x80000000 | 21, QMC::DefaultPropertyFlags | QMC::EnumOrFlag, 0),
        // property 'currentSession'
        QtMocHelpers::PropertyData<ChatSession*>(22, 0x80000000 | 9, QMC::DefaultPropertyFlags | QMC::Writable | QMC::EnumOrFlag | QMC::StdCppSet | QMC::Bindable, 1),
        // property 'sessionCount'
        QtMocHelpers::PropertyData<int>(23, QMetaType::Int, QMC::DefaultPropertyFlags, 2),
        // property 'searchText'
        QtMocHelpers::PropertyData<QString>(24, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet | QMC::Bindable, 3),
    };
    QtMocHelpers::UintData qt_enums {
    };
    QtMocHelpers::UintData qt_constructors {};
    QtMocHelpers::ClassInfos qt_classinfo({
            {    1,    2 },
    });
    return QtMocHelpers::metaObjectData<SessionListViewModel, void>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums, qt_constructors, qt_classinfo);
}
Q_CONSTINIT const QMetaObject SessionListViewModel::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN20SessionListViewModelE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN20SessionListViewModelE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN20SessionListViewModelE_t>.metaTypes,
    nullptr
} };

void SessionListViewModel::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<SessionListViewModel *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->sessionsChanged(); break;
        case 1: _t->currentSessionChanged(); break;
        case 2: _t->sessionCountChanged(); break;
        case 3: _t->searchTextChanged(); break;
        case 4: _t->sessionSelected((*reinterpret_cast< std::add_pointer_t<ChatSession*>>(_a[1]))); break;
        case 5: { ChatSession* _r = _t->createNewSession();
            if (_a[0]) *reinterpret_cast< ChatSession**>(_a[0]) = std::move(_r); }  break;
        case 6: { ChatSession* _r = _t->createNewSession((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< ChatSession**>(_a[0]) = std::move(_r); }  break;
        case 7: _t->deleteSession((*reinterpret_cast< std::add_pointer_t<ChatSession*>>(_a[1]))); break;
        case 8: _t->deleteSession((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 9: { ChatSession* _r = _t->getSession((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])));
            if (_a[0]) *reinterpret_cast< ChatSession**>(_a[0]) = std::move(_r); }  break;
        case 10: _t->selectSession((*reinterpret_cast< std::add_pointer_t<ChatSession*>>(_a[1]))); break;
        case 11: _t->selectSession((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 12: { QList<ChatSession*> _r = _t->getFilteredSessions();
            if (_a[0]) *reinterpret_cast< QList<ChatSession*>*>(_a[0]) = std::move(_r); }  break;
        case 13: _t->initializeWithSampleData(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
        case 4:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< ChatSession* >(); break;
            }
            break;
        case 7:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< ChatSession* >(); break;
            }
            break;
        case 10:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< ChatSession* >(); break;
            }
            break;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (SessionListViewModel::*)()>(_a, &SessionListViewModel::sessionsChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (SessionListViewModel::*)()>(_a, &SessionListViewModel::currentSessionChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (SessionListViewModel::*)()>(_a, &SessionListViewModel::sessionCountChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (SessionListViewModel::*)()>(_a, &SessionListViewModel::searchTextChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (SessionListViewModel::*)(ChatSession * )>(_a, &SessionListViewModel::sessionSelected, 4))
            return;
    }
    if (_c == QMetaObject::RegisterPropertyMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 1:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< ChatSession* >(); break;
        }
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<QQmlListProperty<ChatSession>*>(_v) = _t->sessions(); break;
        case 1: *reinterpret_cast<ChatSession**>(_v) = _t->currentSession(); break;
        case 2: *reinterpret_cast<int*>(_v) = _t->sessionCount(); break;
        case 3: *reinterpret_cast<QString*>(_v) = _t->searchText(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 1: _t->setCurrentSession(*reinterpret_cast<ChatSession**>(_v)); break;
        case 3: _t->setSearchText(*reinterpret_cast<QString*>(_v)); break;
        default: break;
        }
    }
    if (_c == QMetaObject::BindableProperty) {
        switch (_id) {
        case 1: *static_cast<QUntypedBindable *>(_a[0]) = _t->bindableCurrentSession(); break;
        case 3: *static_cast<QUntypedBindable *>(_a[0]) = _t->bindableSearchText(); break;
        default: break;
        }
    }
}

const QMetaObject *SessionListViewModel::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *SessionListViewModel::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN20SessionListViewModelE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int SessionListViewModel::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 14)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 14;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 14)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 14;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void SessionListViewModel::sessionsChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void SessionListViewModel::currentSessionChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void SessionListViewModel::sessionCountChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void SessionListViewModel::searchTextChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void SessionListViewModel::sessionSelected(ChatSession * _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1);
}
QT_WARNING_POP
