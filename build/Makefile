# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake

# The command to remove a file.
RM = /Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/X/soloholic/ChamberUI3

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/X/soloholic/ChamberUI3/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles /Users/<USER>/X/soloholic/ChamberUI3/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/X/soloholic/ChamberUI3/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named ChamberUI

# Build rule for target.
ChamberUI: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ChamberUI
.PHONY : ChamberUI

# fast build rule for target.
ChamberUI/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/build
.PHONY : ChamberUI/fast

#=============================================================================
# Target rules for targets named ChamberUI_qmlimportscan

# Build rule for target.
ChamberUI_qmlimportscan: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ChamberUI_qmlimportscan
.PHONY : ChamberUI_qmlimportscan

# fast build rule for target.
ChamberUI_qmlimportscan/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI_qmlimportscan.dir/build.make CMakeFiles/ChamberUI_qmlimportscan.dir/build
.PHONY : ChamberUI_qmlimportscan/fast

#=============================================================================
# Target rules for targets named ChamberUI_autogen_timestamp_deps

# Build rule for target.
ChamberUI_autogen_timestamp_deps: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ChamberUI_autogen_timestamp_deps
.PHONY : ChamberUI_autogen_timestamp_deps

# fast build rule for target.
ChamberUI_autogen_timestamp_deps/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/build.make CMakeFiles/ChamberUI_autogen_timestamp_deps.dir/build
.PHONY : ChamberUI_autogen_timestamp_deps/fast

#=============================================================================
# Target rules for targets named ChamberUI_autogen

# Build rule for target.
ChamberUI_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ChamberUI_autogen
.PHONY : ChamberUI_autogen

# fast build rule for target.
ChamberUI_autogen/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI_autogen.dir/build.make CMakeFiles/ChamberUI_autogen.dir/build
.PHONY : ChamberUI_autogen/fast

ChamberUI_autogen/EWIEGA46WW/qrc_qml.o: ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.o
.PHONY : ChamberUI_autogen/EWIEGA46WW/qrc_qml.o

# target to build an object file
ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.o
.PHONY : ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.o

ChamberUI_autogen/EWIEGA46WW/qrc_qml.i: ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.i
.PHONY : ChamberUI_autogen/EWIEGA46WW/qrc_qml.i

# target to preprocess a source file
ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.i
.PHONY : ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.i

ChamberUI_autogen/EWIEGA46WW/qrc_qml.s: ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.s
.PHONY : ChamberUI_autogen/EWIEGA46WW/qrc_qml.s

# target to generate assembly for a file
ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.s
.PHONY : ChamberUI_autogen/EWIEGA46WW/qrc_qml.cpp.s

ChamberUI_autogen/mocs_compilation.o: ChamberUI_autogen/mocs_compilation.cpp.o
.PHONY : ChamberUI_autogen/mocs_compilation.o

# target to build an object file
ChamberUI_autogen/mocs_compilation.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/ChamberUI_autogen/mocs_compilation.cpp.o
.PHONY : ChamberUI_autogen/mocs_compilation.cpp.o

ChamberUI_autogen/mocs_compilation.i: ChamberUI_autogen/mocs_compilation.cpp.i
.PHONY : ChamberUI_autogen/mocs_compilation.i

# target to preprocess a source file
ChamberUI_autogen/mocs_compilation.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/ChamberUI_autogen/mocs_compilation.cpp.i
.PHONY : ChamberUI_autogen/mocs_compilation.cpp.i

ChamberUI_autogen/mocs_compilation.s: ChamberUI_autogen/mocs_compilation.cpp.s
.PHONY : ChamberUI_autogen/mocs_compilation.s

# target to generate assembly for a file
ChamberUI_autogen/mocs_compilation.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/ChamberUI_autogen/mocs_compilation.cpp.s
.PHONY : ChamberUI_autogen/mocs_compilation.cpp.s

build/.qt/rcc/qrc_qml_resources.o: build/.qt/rcc/qrc_qml_resources.cpp.o
.PHONY : build/.qt/rcc/qrc_qml_resources.o

# target to build an object file
build/.qt/rcc/qrc_qml_resources.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/build/.qt/rcc/qrc_qml_resources.cpp.o
.PHONY : build/.qt/rcc/qrc_qml_resources.cpp.o

build/.qt/rcc/qrc_qml_resources.i: build/.qt/rcc/qrc_qml_resources.cpp.i
.PHONY : build/.qt/rcc/qrc_qml_resources.i

# target to preprocess a source file
build/.qt/rcc/qrc_qml_resources.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/build/.qt/rcc/qrc_qml_resources.cpp.i
.PHONY : build/.qt/rcc/qrc_qml_resources.cpp.i

build/.qt/rcc/qrc_qml_resources.s: build/.qt/rcc/qrc_qml_resources.cpp.s
.PHONY : build/.qt/rcc/qrc_qml_resources.s

# target to generate assembly for a file
build/.qt/rcc/qrc_qml_resources.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/build/.qt/rcc/qrc_qml_resources.cpp.s
.PHONY : build/.qt/rcc/qrc_qml_resources.cpp.s

src/main.o: src/main.cc.o
.PHONY : src/main.o

# target to build an object file
src/main.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/src/main.cc.o
.PHONY : src/main.cc.o

src/main.i: src/main.cc.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/src/main.cc.i
.PHONY : src/main.cc.i

src/main.s: src/main.cc.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/src/main.cc.s
.PHONY : src/main.cc.s

src/models/ChatMessage.o: src/models/ChatMessage.cpp.o
.PHONY : src/models/ChatMessage.o

# target to build an object file
src/models/ChatMessage.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/src/models/ChatMessage.cpp.o
.PHONY : src/models/ChatMessage.cpp.o

src/models/ChatMessage.i: src/models/ChatMessage.cpp.i
.PHONY : src/models/ChatMessage.i

# target to preprocess a source file
src/models/ChatMessage.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/src/models/ChatMessage.cpp.i
.PHONY : src/models/ChatMessage.cpp.i

src/models/ChatMessage.s: src/models/ChatMessage.cpp.s
.PHONY : src/models/ChatMessage.s

# target to generate assembly for a file
src/models/ChatMessage.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/src/models/ChatMessage.cpp.s
.PHONY : src/models/ChatMessage.cpp.s

src/models/ChatSession.o: src/models/ChatSession.cpp.o
.PHONY : src/models/ChatSession.o

# target to build an object file
src/models/ChatSession.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/src/models/ChatSession.cpp.o
.PHONY : src/models/ChatSession.cpp.o

src/models/ChatSession.i: src/models/ChatSession.cpp.i
.PHONY : src/models/ChatSession.i

# target to preprocess a source file
src/models/ChatSession.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/src/models/ChatSession.cpp.i
.PHONY : src/models/ChatSession.cpp.i

src/models/ChatSession.s: src/models/ChatSession.cpp.s
.PHONY : src/models/ChatSession.s

# target to generate assembly for a file
src/models/ChatSession.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/src/models/ChatSession.cpp.s
.PHONY : src/models/ChatSession.cpp.s

src/viewmodels/ChatViewModel.o: src/viewmodels/ChatViewModel.cpp.o
.PHONY : src/viewmodels/ChatViewModel.o

# target to build an object file
src/viewmodels/ChatViewModel.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/src/viewmodels/ChatViewModel.cpp.o
.PHONY : src/viewmodels/ChatViewModel.cpp.o

src/viewmodels/ChatViewModel.i: src/viewmodels/ChatViewModel.cpp.i
.PHONY : src/viewmodels/ChatViewModel.i

# target to preprocess a source file
src/viewmodels/ChatViewModel.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/src/viewmodels/ChatViewModel.cpp.i
.PHONY : src/viewmodels/ChatViewModel.cpp.i

src/viewmodels/ChatViewModel.s: src/viewmodels/ChatViewModel.cpp.s
.PHONY : src/viewmodels/ChatViewModel.s

# target to generate assembly for a file
src/viewmodels/ChatViewModel.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/src/viewmodels/ChatViewModel.cpp.s
.PHONY : src/viewmodels/ChatViewModel.cpp.s

src/viewmodels/SessionListViewModel.o: src/viewmodels/SessionListViewModel.cpp.o
.PHONY : src/viewmodels/SessionListViewModel.o

# target to build an object file
src/viewmodels/SessionListViewModel.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/src/viewmodels/SessionListViewModel.cpp.o
.PHONY : src/viewmodels/SessionListViewModel.cpp.o

src/viewmodels/SessionListViewModel.i: src/viewmodels/SessionListViewModel.cpp.i
.PHONY : src/viewmodels/SessionListViewModel.i

# target to preprocess a source file
src/viewmodels/SessionListViewModel.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/src/viewmodels/SessionListViewModel.cpp.i
.PHONY : src/viewmodels/SessionListViewModel.cpp.i

src/viewmodels/SessionListViewModel.s: src/viewmodels/SessionListViewModel.cpp.s
.PHONY : src/viewmodels/SessionListViewModel.s

# target to generate assembly for a file
src/viewmodels/SessionListViewModel.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ChamberUI.dir/build.make CMakeFiles/ChamberUI.dir/src/viewmodels/SessionListViewModel.cpp.s
.PHONY : src/viewmodels/SessionListViewModel.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... ChamberUI_autogen"
	@echo "... ChamberUI_autogen_timestamp_deps"
	@echo "... ChamberUI_qmlimportscan"
	@echo "... ChamberUI"
	@echo "... ChamberUI_autogen/EWIEGA46WW/qrc_qml.o"
	@echo "... ChamberUI_autogen/EWIEGA46WW/qrc_qml.i"
	@echo "... ChamberUI_autogen/EWIEGA46WW/qrc_qml.s"
	@echo "... ChamberUI_autogen/mocs_compilation.o"
	@echo "... ChamberUI_autogen/mocs_compilation.i"
	@echo "... ChamberUI_autogen/mocs_compilation.s"
	@echo "... build/.qt/rcc/qrc_qml_resources.o"
	@echo "... build/.qt/rcc/qrc_qml_resources.i"
	@echo "... build/.qt/rcc/qrc_qml_resources.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
	@echo "... src/models/ChatMessage.o"
	@echo "... src/models/ChatMessage.i"
	@echo "... src/models/ChatMessage.s"
	@echo "... src/models/ChatSession.o"
	@echo "... src/models/ChatSession.i"
	@echo "... src/models/ChatSession.s"
	@echo "... src/viewmodels/ChatViewModel.o"
	@echo "... src/viewmodels/ChatViewModel.i"
	@echo "... src/viewmodels/ChatViewModel.s"
	@echo "... src/viewmodels/SessionListViewModel.o"
	@echo "... src/viewmodels/SessionListViewModel.i"
	@echo "... src/viewmodels/SessionListViewModel.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

