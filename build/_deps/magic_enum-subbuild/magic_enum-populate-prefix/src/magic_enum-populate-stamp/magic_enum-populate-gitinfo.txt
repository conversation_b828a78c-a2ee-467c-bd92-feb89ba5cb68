# This is a generated file and its contents are an internal implementation detail.
# The download step will be re-executed if anything in this file changes.
# No other meaning or use of this file is supported.

method=git
command=/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake;-DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE;-P;/Users/<USER>/X/soloholic/ChamberUI3/build/_deps/magic_enum-subbuild/magic_enum-populate-prefix/tmp/magic_enum-populate-gitclone.cmake
source_dir=/Users/<USER>/X/soloholic/ChamberUI3/build/_deps/magic_enum-src
work_dir=/Users/<USER>/X/soloholic/ChamberUI3/build/_deps
repository=https://github.com/Neargye/magic_enum.git
remote=origin
init_submodules=TRUE
recurse_submodules=--recursive
submodules=
CMP0097=NEW
      
