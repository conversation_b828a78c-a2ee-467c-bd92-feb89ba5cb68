import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../styles"

Rectangle {
    id: root
    
    property alias model: listView.model
    property alias currentSession: listView.currentItem
    
    signal sessionSelected(var session)
    signal newChatRequested()
    
    color: Colors.sidebarBackground
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 12
        spacing: 16
        
        // Header with search and new chat
        ColumnLayout {
            Layout.fillWidth: true
            spacing: 12
            
            // Search input
            Rectangle {
                Layout.fillWidth: true
                height: 36
                color: Colors.inputBackground
                border.color: searchInput.activeFocus ? Colors.inputFocusBorder : Colors.inputBorder
                border.width: 1
                radius: 8
                
                TextInput {
                    id: searchInput
                    anchors.fill: parent
                    anchors.leftMargin: 12
                    anchors.rightMargin: 12
                    verticalAlignment: TextInput.AlignVCenter
                    
                    color: Colors.primaryText
                    font.family: Typography.primaryFont
                    font.pixelSize: Typography.bodyMedium
                    
                    Text {
                        anchors.left: parent.left
                        anchors.verticalCenter: parent.verticalCenter
                        text: "Search"
                        color: Colors.placeholderText
                        font: parent.font
                        visible: parent.text.length === 0 && !parent.activeFocus
                    }
                }
            }
            
            // New Chat button
            Button {
                Layout.fillWidth: true
                height: 40
                
                background: Rectangle {
                    color: parent.hovered ? Colors.primaryHover : Colors.primary
                    radius: 8
                    
                    Behavior on color {
                        ColorAnimation { duration: 150 }
                    }
                }
                
                contentItem: Text {
                    text: "New Chat"
                    color: Colors.primaryText
                    font.family: Typography.primaryFont
                    font.pixelSize: Typography.bodyMedium
                    font.weight: Typography.medium
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                
                onClicked: root.newChatRequested()
            }
        }
        
        // Sessions list
        ListView {
            id: listView
            Layout.fillWidth: true
            Layout.fillHeight: true
            
            spacing: 4
            clip: true
            
            delegate: Rectangle {
                width: listView.width
                height: 64
                color: {
                    if (ListView.isCurrentItem) return Colors.sidebarItemSelected
                    if (mouseArea.containsMouse) return Colors.sidebarItemHover
                    return "transparent"
                }
                radius: 8
                
                Behavior on color {
                    ColorAnimation { duration: 150 }
                }
                
                MouseArea {
                    id: mouseArea
                    anchors.fill: parent
                    hoverEnabled: true
                    
                    onClicked: {
                        listView.currentIndex = index
                        root.sessionSelected(model.modelData || model)
                    }
                }
                
                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 12
                    spacing: 4
                    
                    Text {
                        Layout.fillWidth: true
                        text: model.title || (model.modelData ? model.modelData.title : "")
                        color: Colors.primaryText
                        font.family: Typography.primaryFont
                        font.pixelSize: Typography.bodyMedium
                        font.weight: Typography.medium
                        elide: Text.ElideRight
                        maximumLineCount: 1
                    }
                    
                    Text {
                        Layout.fillWidth: true
                        text: {
                            var session = model.modelData || model
                            return session ? session.getPreviewText() : ""
                        }
                        color: Colors.secondaryText
                        font.family: Typography.primaryFont
                        font.pixelSize: Typography.bodySmall
                        elide: Text.ElideRight
                        maximumLineCount: 2
                        wrapMode: Text.WordWrap
                    }
                }
            }
        }
    }
}
