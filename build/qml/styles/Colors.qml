pragma Singleton
import QtQuick 2.15

QtObject {
    // Background colors
    readonly property color background: "#1a1a1a"
    readonly property color surfaceBackground: "#2d2d2d"
    readonly property color cardBackground: "#3a3a3a"
    readonly property color hoverBackground: "#4a4a4a"
    readonly property color selectedBackground: "#404040"
    
    // Text colors
    readonly property color primaryText: "#ffffff"
    readonly property color secondaryText: "#b0b0b0"
    readonly property color mutedText: "#808080"
    readonly property color placeholderText: "#606060"
    
    // Accent colors
    readonly property color primary: "#4285f4"
    readonly property color primaryHover: "#3367d6"
    readonly property color primaryPressed: "#2851a3"
    
    // Message colors
    readonly property color userMessageBackground: "#4285f4"
    readonly property color aiMessageBackground: "#3a3a3a"
    readonly property color userMessageText: "#ffffff"
    readonly property color aiMessageText: "#ffffff"
    
    // Border colors
    readonly property color border: "#404040"
    readonly property color focusBorder: "#4285f4"
    readonly property color divider: "#2d2d2d"
    
    // Status colors
    readonly property color success: "#34a853"
    readonly property color warning: "#fbbc04"
    readonly property color error: "#ea4335"
    readonly property color info: "#4285f4"
    
    // Input colors
    readonly property color inputBackground: "#2d2d2d"
    readonly property color inputBorder: "#404040"
    readonly property color inputFocusBorder: "#4285f4"
    
    // Sidebar colors
    readonly property color sidebarBackground: "#1e1e1e"
    readonly property color sidebarItemHover: "#2d2d2d"
    readonly property color sidebarItemSelected: "#3a3a3a"
    
    // Chat colors
    readonly property color chatBackground: "#1a1a1a"
    readonly property color messageHover: "#252525"
    
    // Scrollbar colors
    readonly property color scrollbarTrack: "#2d2d2d"
    readonly property color scrollbarHandle: "#4a4a4a"
    readonly property color scrollbarHandleHover: "#5a5a5a"
}
