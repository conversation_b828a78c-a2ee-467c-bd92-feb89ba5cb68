#include <QGuiApplication>
#include <QQmlApplicationEngine>
#include <QQmlContext>
#include <QFontDatabase>
#include <QFile>
#include <QDebug>

// Include model and viewmodel headers
#include "models/ChatMessage.h"
#include "models/ChatSession.h"
#include "viewmodels/ChatViewModel.h"
#include "viewmodels/SessionListViewModel.h"

int main(int argc, char *argv[])
{
    QGuiApplication app(argc, argv);

    // Set application information
    app.setOrganizationName("Soloholic");
    app.setOrganizationDomain("soloholic.com");
    app.setApplicationName("ChamberUI");
    app.setApplicationVersion("1.0.0");

    // Register QML types
    qmlRegisterType<ChatMessage>("ChamberUI", 1, 0, "ChatMessage");
    qmlRegisterType<ChatSession>("ChamberUI", 1, 0, "ChatSession");
    qmlRegisterType<ChatViewModel>("ChamberUI", 1, 0, "ChatViewModel");
    qmlRegisterType<SessionListViewModel>("ChamberUI", 1, 0, "SessionListViewModel");

    // Set up QML engine
    QQmlApplicationEngine engine;

    // Load main QML file
    // Try different approaches to find the QML file
    QUrl url{QStringLiteral("qrc:/qml/main.qml")};

    QObject::connect(&engine, &QQmlApplicationEngine::objectCreated,
                     &app, [url](QObject *obj, const QUrl &objUrl) {
        if (!obj && url == objUrl)
            QCoreApplication::exit(-1);
    }, Qt::QueuedConnection);

    engine.load(url);

    return app.exec();
}