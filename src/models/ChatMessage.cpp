#include "ChatMessage.h"

ChatMessage::ChatMessage(QObject *parent)
    : QObject(parent)
    , m_content("")
    , m_isUser(false)
    , m_timestamp(QDateTime::currentDateTime())
    , m_status(MessageStatus::Received)
{
}

ChatMessage::ChatMessage(const QString &content, bool isUser, QObject *parent)
    : QObject(parent)
    , m_content(content)
    , m_isUser(isUser)
    , m_timestamp(QDateTime::currentDateTime())
    , m_status(isUser ? MessageStatus::Sending : MessageStatus::Received)
{
}

void ChatMessage::setContent(const QString &content)
{
    m_content = content;
}

void ChatMessage::setIsUser(bool isUser)
{
    m_isUser = isUser;
}

void ChatMessage::setTimestamp(const QDateTime &timestamp)
{
    m_timestamp = timestamp;
}

void ChatMessage::setStatus(MessageStatus status)
{
    m_status = status;
}
