#pragma once

#include <QObject>
#include <QString>
#include <QDateTime>
#include <QProperty>
#include <QQmlEngine>

class ChatMessage : public QObject
{
    Q_OBJECT
    QML_ELEMENT

    Q_PROPERTY(QString content READ content WRITE setContent NOTIFY contentChanged BINDABLE bindableContent)
    Q_PROPERTY(bool isUser READ isUser WRITE setIsUser NOTIFY isUserChanged BINDABLE bindableIsUser)
    Q_PROPERTY(QDateTime timestamp READ timestamp WRITE setTimestamp NOTIFY timestampChanged BINDABLE bindableTimestamp)
    Q_PROPERTY(MessageStatus status READ status WRITE setStatus NOTIFY statusChanged BINDABLE bindableStatus)

public:
    enum class MessageStatus {
        Sending,
        Sent,
        Received,
        Error
    };
    Q_ENUM(MessageStatus)

    explicit ChatMessage(QObject *parent = nullptr);
    ChatMessage(const QString &content, bool isUser, QObject *parent = nullptr);

    // Property getters
    QString content() const { return m_content; }
    bool isUser() const { return m_isUser; }
    QDateTime timestamp() const { return m_timestamp; }
    MessageStatus status() const { return m_status; }

    // Property setters
    void setContent(const QString &content);
    void setIsUser(bool isUser);
    void setTimestamp(const QDateTime &timestamp);
    void setStatus(MessageStatus status);

    // Bindable properties
    QBindable<QString> bindableContent() { return &m_content; }
    QBindable<bool> bindableIsUser() { return &m_isUser; }
    QBindable<QDateTime> bindableTimestamp() { return &m_timestamp; }
    QBindable<MessageStatus> bindableStatus() { return &m_status; }

signals:
    void contentChanged();
    void isUserChanged();
    void timestampChanged();
    void statusChanged();

private:
    Q_OBJECT_BINDABLE_PROPERTY(ChatMessage, QString, m_content, &ChatMessage::contentChanged)
    Q_OBJECT_BINDABLE_PROPERTY(ChatMessage, bool, m_isUser, &ChatMessage::isUserChanged)
    Q_OBJECT_BINDABLE_PROPERTY(ChatMessage, QDateTime, m_timestamp, &ChatMessage::timestampChanged)
    Q_OBJECT_BINDABLE_PROPERTY(ChatMessage, MessageStatus, m_status, &ChatMessage::statusChanged)
};
