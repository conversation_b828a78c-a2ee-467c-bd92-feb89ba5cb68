#include "ChatViewModel.h"
#include <QRandomGenerator>

ChatViewModel::ChatViewModel(QObject *parent)
    : QObject(parent)
    , m_currentSession(nullptr)
    , m_inputText("")
    , m_isTyping(false)
    , m_typingTimer(new QTimer(this))
{
    m_typingTimer->setSingleShot(true);
    connect(m_typingTimer, &QTimer::timeout, this, &ChatViewModel::onTypingTimer);
    
    // Connect input text changes to canSend updates
    connect(this, &ChatViewModel::inputTextChanged, this, &ChatViewModel::updateCanSend);
    connect(this, &ChatViewModel::currentSessionChanged, this, &ChatViewModel::updateCanSend);
}

bool ChatViewModel::canSend() const
{
    return m_canSend;
}

void ChatViewModel::setCurrentSession(ChatSession* session)
{
    m_currentSession = session;
}

void ChatViewModel::setInputText(const QString &text)
{
    m_inputText = text;
}

void ChatViewModel::sendMessage()
{
    sendMessage(m_inputText);
}

void ChatViewModel::sendMessage(const QString &content)
{
    if (content.trimmed().isEmpty() || !m_currentSession) {
        return;
    }

    // Create user message
    auto userMessage = new ChatMessage(content.trimmed(), true, this);
    userMessage->setStatus(ChatMessage::MessageStatus::Sent);
    m_currentSession->addMessage(userMessage);

    // Clear input
    clearInput();

    // Simulate AI response after a delay
    QTimer::singleShot(1000, this, [this, content]() {
        simulateAIResponse(content);
    });
}

void ChatViewModel::clearInput()
{
    setInputText("");
}

void ChatViewModel::simulateTyping()
{
    if (m_isTyping) return;
    
    m_isTyping = true;
    m_typingTimer->start(2000 + QRandomGenerator::global()->bounded(2000)); // 2-4 seconds
}

void ChatViewModel::onTypingTimer()
{
    m_isTyping = false;
}

void ChatViewModel::simulateAIResponse(const QString &userMessage)
{
    if (!m_currentSession) return;

    // Start typing indicator
    simulateTyping();

    // Generate a simple AI response after typing
    QTimer::singleShot(2500, this, [this, userMessage]() {
        if (!m_currentSession) return;

        QString response;
        
        // Simple response generation based on user input
        if (userMessage.contains("hello", Qt::CaseInsensitive) || 
            userMessage.contains("hi", Qt::CaseInsensitive)) {
            response = "Hello! How can I help you today?";
        } else if (userMessage.contains("how are you", Qt::CaseInsensitive)) {
            response = "I'm doing well, thank you for asking! I'm here to help you with any questions you might have.";
        } else if (userMessage.contains("what", Qt::CaseInsensitive) && 
                   userMessage.contains("name", Qt::CaseInsensitive)) {
            response = "I'm ChamberUI Assistant, your AI companion. What would you like to know?";
        } else {
            QStringList responses = {
                "That's an interesting question. Let me think about that...",
                "I understand what you're asking. Here's my perspective on that:",
                "Thanks for sharing that with me. I'd be happy to help you explore this topic further.",
                "That's a great point. Let me provide you with some insights:",
                "I appreciate you bringing this up. Here's what I think about it:"
            };
            response = responses[QRandomGenerator::global()->bounded(responses.size())];
            response += "\n\nCould you provide more details so I can give you a more specific answer?";
        }

        auto aiMessage = new ChatMessage(response, false, this);
        aiMessage->setStatus(ChatMessage::MessageStatus::Received);
        m_currentSession->addMessage(aiMessage);
        
        emit messageReceived(aiMessage);
    });
}

void ChatViewModel::updateCanSend()
{
    bool newCanSend = !m_inputText.trimmed().isEmpty() && m_currentSession != nullptr;
    if (m_canSend != newCanSend) {
        m_canSend = newCanSend;
        emit canSendChanged();
    }
}
