#pragma once

#include <QObject>
#include <QString>
#include <QProperty>
#include <QQmlEngine>
#include <QQmlListProperty>
#include <QTimer>
#include "../models/ChatSession.h"
#include "../models/ChatMessage.h"

class ChatViewModel : public QObject
{
    Q_OBJECT
    QML_ELEMENT

    Q_PROPERTY(ChatSession* currentSession READ currentSession WRITE setCurrentSession NOTIFY currentSessionChanged BINDABLE bindableCurrentSession)
    Q_PROPERTY(QString inputText READ inputText WRITE setInputText NOTIFY inputTextChanged BINDABLE bindableInputText)
    Q_PROPERTY(bool isTyping READ isTyping NOTIFY isTypingChanged BINDABLE bindableIsTyping)
    Q_PROPERTY(bool canSend READ canSend NOTIFY canSendChanged)

public:
    explicit ChatViewModel(QObject *parent = nullptr);

    // Property getters
    ChatSession* currentSession() const { return m_currentSession; }
    QString inputText() const { return m_inputText; }
    bool isTyping() const { return m_isTyping; }
    bool canSend() const;

    // Property setters
    void setCurrentSession(ChatSession* session);
    void setInputText(const QString &text);

    // Bindable properties
    QBindable<ChatSession*> bindableCurrentSession() { return &m_currentSession; }
    QBindable<QString> bindableInputText() { return &m_inputText; }
    QBindable<bool> bindableIsTyping() { return &m_isTyping; }

    // Public methods
    Q_INVOKABLE void sendMessage();
    Q_INVOKABLE void sendMessage(const QString &content);
    Q_INVOKABLE void clearInput();
    Q_INVOKABLE void simulateTyping();

signals:
    void currentSessionChanged();
    void inputTextChanged();
    void isTypingChanged();
    void canSendChanged();
    void messageReceived(ChatMessage* message);

private slots:
    void onTypingTimer();
    void simulateAIResponse(const QString &userMessage);

private:
    void updateCanSend();

    Q_OBJECT_BINDABLE_PROPERTY(ChatViewModel, ChatSession*, m_currentSession, &ChatViewModel::currentSessionChanged)
    Q_OBJECT_BINDABLE_PROPERTY(ChatViewModel, QString, m_inputText, &ChatViewModel::inputTextChanged)
    Q_OBJECT_BINDABLE_PROPERTY(ChatViewModel, bool, m_isTyping, &ChatViewModel::isTypingChanged)
    
    QTimer* m_typingTimer;
    bool m_canSend = false;
};
