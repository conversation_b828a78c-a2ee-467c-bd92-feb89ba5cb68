#include "SessionListViewModel.h"
#include "../models/ChatMessage.h"

SessionListViewModel::SessionListViewModel(QObject *parent)
    : QObject(parent)
    , m_currentSession(nullptr)
    , m_searchText("")
{
    // Connect search text changes to sessions changed signal for filtering
    connect(this, &SessionListViewModel::searchTextChanged, this, &SessionListViewModel::sessionsChanged);
}

QQmlListProperty<ChatSession> SessionListViewModel::sessions()
{
    return QQmlListProperty<ChatSession>(this, &m_sessionsList,
                                        &SessionListViewModel::appendSession,
                                        &SessionListViewModel::sessionCount,
                                        &SessionListViewModel::sessionAt,
                                        &SessionListViewModel::clearSessions);
}

void SessionListViewModel::setCurrentSession(ChatSession* session)
{
    if (m_currentSession != session) {
        m_currentSession = session;
        emit sessionSelected(session);
    }
}

void SessionListViewModel::setSearchText(const QString &text)
{
    m_searchText = text;
}

ChatSession* SessionListViewModel::createNewSession()
{
    return createNewSession("New Chat");
}

ChatSession* SessionListViewModel::createNewSession(const QString &title)
{
    auto session = new ChatSession(title, this);
    addSession(session);
    setCurrentSession(session);
    return session;
}

void SessionListViewModel::deleteSession(ChatSession* session)
{
    if (!session) return;
    
    int index = m_sessionsList.indexOf(session);
    if (index >= 0) {
        deleteSession(index);
    }
}

void SessionListViewModel::deleteSession(int index)
{
    if (index < 0 || index >= m_sessionsList.size()) return;
    
    auto session = m_sessionsList.at(index);
    
    // If deleting current session, select another one
    if (session == m_currentSession) {
        if (m_sessionsList.size() > 1) {
            // Select the next session, or previous if this is the last one
            int newIndex = (index < m_sessionsList.size() - 1) ? index : index - 1;
            if (newIndex >= 0 && newIndex < m_sessionsList.size() && newIndex != index) {
                setCurrentSession(m_sessionsList.at(newIndex));
            }
        } else {
            setCurrentSession(nullptr);
        }
    }
    
    m_sessionsList.removeAt(index);
    session->deleteLater();
    
    emit sessionsChanged();
    emit sessionCountChanged();
}

ChatSession* SessionListViewModel::getSession(int index) const
{
    if (index >= 0 && index < m_sessionsList.size()) {
        return m_sessionsList.at(index);
    }
    return nullptr;
}

void SessionListViewModel::selectSession(ChatSession* session)
{
    setCurrentSession(session);
}

void SessionListViewModel::selectSession(int index)
{
    auto session = getSession(index);
    if (session) {
        setCurrentSession(session);
    }
}

QList<ChatSession*> SessionListViewModel::getFilteredSessions() const
{
    if (m_searchText.value().trimmed().isEmpty()) {
        return m_sessionsList;
    }

    QList<ChatSession*> filtered;
    for (auto session : m_sessionsList) {
        if (matchesSearch(session)) {
            filtered.append(session);
        }
    }
    return filtered;
}

void SessionListViewModel::initializeWithSampleData()
{
    // Create some sample sessions
    auto session1 = createNewSession("Qt6 C++ 编程学习");
    auto msg1 = new ChatMessage("你好，我想学习Qt6和QML开发", true, session1);
    session1->addMessage(msg1);
    auto reply1 = new ChatMessage("很高兴帮助你学习Qt6！Qt6是一个强大的跨平台应用程序开发框架。你想从哪个方面开始学习呢？", false, session1);
    session1->addMessage(reply1);
    
    auto session2 = createNewSession("独立产品运营分析");
    auto msg2 = new ChatMessage("请帮我分析一下独立开发者如何做产品运营", true, session2);
    session2->addMessage(msg2);
    
    auto session3 = createNewSession("文章总结分析与研究");
    auto msg3 = new ChatMessage("能帮我总结一篇技术文章的要点吗？", true, session3);
    session3->addMessage(msg3);
    
    auto session4 = createNewSession("早期问题咨询");
    
    auto session5 = createNewSession("Green Tea GC 开源讨论");
    auto msg5 = new ChatMessage("我想了解一下Green Tea垃圾收集器的工作原理", true, session5);
    session5->addMessage(msg5);
    
    auto session6 = createNewSession("项目防御流程规划");
    
    // Set the first session as current
    setCurrentSession(session1);
}

// Static list property helpers
void SessionListViewModel::appendSession(QQmlListProperty<ChatSession> *list, ChatSession *session)
{
    auto viewModel = qobject_cast<SessionListViewModel*>(list->object);
    if (viewModel) {
        viewModel->addSession(session);
    }
}

qsizetype SessionListViewModel::sessionCount(QQmlListProperty<ChatSession> *list)
{
    auto viewModel = qobject_cast<SessionListViewModel*>(list->object);
    return viewModel ? viewModel->getFilteredSessions().size() : 0;
}

ChatSession *SessionListViewModel::sessionAt(QQmlListProperty<ChatSession> *list, qsizetype index)
{
    auto viewModel = qobject_cast<SessionListViewModel*>(list->object);
    if (viewModel) {
        auto filtered = viewModel->getFilteredSessions();
        return (index >= 0 && index < filtered.size()) ? filtered.at(static_cast<int>(index)) : nullptr;
    }
    return nullptr;
}

void SessionListViewModel::clearSessions(QQmlListProperty<ChatSession> *list)
{
    auto viewModel = qobject_cast<SessionListViewModel*>(list->object);
    if (viewModel) {
        qDeleteAll(viewModel->m_sessionsList);
        viewModel->m_sessionsList.clear();
        viewModel->setCurrentSession(nullptr);
        emit viewModel->sessionsChanged();
        emit viewModel->sessionCountChanged();
    }
}

void SessionListViewModel::addSession(ChatSession* session)
{
    if (!session) return;
    
    session->setParent(this);
    m_sessionsList.prepend(session); // Add new sessions at the top
    emit sessionsChanged();
    emit sessionCountChanged();
}

bool SessionListViewModel::matchesSearch(ChatSession* session) const
{
    if (!session || m_searchText.value().trimmed().isEmpty()) {
        return true;
    }

    QString searchLower = m_searchText.value().toLower();
    return session->title().toLower().contains(searchLower) ||
           session->getPreviewText().toLower().contains(searchLower);
}
