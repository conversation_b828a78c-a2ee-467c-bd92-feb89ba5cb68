#pragma once

#include <QObject>
#include <QProperty>
#include <QQmlEngine>
#include <QQmlListProperty>
#include <QList>
#include "../models/ChatSession.h"

class SessionListViewModel : public QObject
{
    Q_OBJECT
    QML_ELEMENT

    Q_PROPERTY(QQmlListProperty<ChatSession> sessions READ sessions NOTIFY sessionsChanged)
    Q_PROPERTY(ChatSession* currentSession READ currentSession WRITE setCurrentSession NOTIFY currentSessionChanged BINDABLE bindableCurrentSession)
    Q_PROPERTY(int sessionCount READ sessionCount NOTIFY sessionCountChanged)
    Q_PROPERTY(QString searchText READ searchText WRITE setSearchText NOTIFY searchTextChanged BINDABLE bindableSearchText)

public:
    explicit SessionListViewModel(QObject *parent = nullptr);

    // Property getters
    QQmlListProperty<ChatSession> sessions();
    ChatSession* currentSession() const { return m_currentSession; }
    int sessionCount() const { return m_sessionsList.size(); }
    QString searchText() const { return m_searchText; }

    // Property setters
    void setCurrentSession(ChatSession* session);
    void setSearchText(const QString &text);

    // Bindable properties
    QBindable<ChatSession*> bindableCurrentSession() { return &m_currentSession; }
    QBindable<QString> bindableSearchText() { return &m_searchText; }

    // Public methods
    Q_INVOKABLE ChatSession* createNewSession();
    Q_INVOKABLE ChatSession* createNewSession(const QString &title);
    Q_INVOKABLE void deleteSession(ChatSession* session);
    Q_INVOKABLE void deleteSession(int index);
    Q_INVOKABLE ChatSession* getSession(int index) const;
    Q_INVOKABLE void selectSession(ChatSession* session);
    Q_INVOKABLE void selectSession(int index);
    Q_INVOKABLE QList<ChatSession*> getFilteredSessions() const;

    // Initialization
    Q_INVOKABLE void initializeWithSampleData();

signals:
    void sessionsChanged();
    void currentSessionChanged();
    void sessionCountChanged();
    void searchTextChanged();
    void sessionSelected(ChatSession* session);

private:
    // List property helpers
    static void appendSession(QQmlListProperty<ChatSession> *list, ChatSession *session);
    static qsizetype sessionCount(QQmlListProperty<ChatSession> *list);
    static ChatSession *sessionAt(QQmlListProperty<ChatSession> *list, qsizetype index);
    static void clearSessions(QQmlListProperty<ChatSession> *list);

    void addSession(ChatSession* session);
    bool matchesSearch(ChatSession* session) const;

    Q_OBJECT_BINDABLE_PROPERTY(SessionListViewModel, ChatSession*, m_currentSession, &SessionListViewModel::currentSessionChanged)
    Q_OBJECT_BINDABLE_PROPERTY(SessionListViewModel, QString, m_searchText, &SessionListViewModel::searchTextChanged)
    
    QList<ChatSession*> m_sessionsList;
};
